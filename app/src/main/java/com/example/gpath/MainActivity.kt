package com.example.gpath

import android.Manifest
import android.content.ContentValues
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.camera.core.Camera
import androidx.camera.core.CameraSelector
import androidx.camera.core.ImageCapture
import androidx.camera.core.ImageCaptureException
import androidx.camera.core.Preview as CameraXPreview
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Button
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.bumptech.glide.integration.compose.ExperimentalGlideComposeApi
import com.bumptech.glide.integration.compose.GlideImage
import com.example.gpath.ui.theme.GpathTheme
import java.text.SimpleDateFormat
import java.util.Locale

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            GpathTheme {
                var borderStatus by remember { mutableStateOf(3) } // 1: Focused, 2: Photo Taken, 3: Live Stream, 4: Error
                var flashMode by remember { mutableStateOf(0) } // 0 for off, 1 for on, 2 for auto
                var imageUri by remember { mutableStateOf<Uri?>(null) }
                val context = LocalContext.current
                var hasCameraPermission by remember {
                    mutableStateOf(
                        ContextCompat.checkSelfPermission(
                            context,
                            Manifest.permission.CAMERA
                        ) == PackageManager.PERMISSION_GRANTED
                    )
                }
                val launcher = rememberLauncherForActivityResult(
                    contract = ActivityResultContracts.RequestPermission(),
                    onResult = { granted ->
                        hasCameraPermission = granted
                    }
                )
                LaunchedEffect(key1 = true) {
                    launcher.launch(Manifest.permission.CAMERA)
                }
                val imageCapture by remember { mutableStateOf<ImageCapture?>(ImageCapture.Builder().build()) }


                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Top
                    ) {
                        CamWinBorder(
                            borderStatus = borderStatus,
                            hasCameraPermission = hasCameraPermission,
                            flashMode = flashMode,
                            imageCapture = imageCapture,
                            imageUri = imageUri,
                            onImageCaptured = { uri ->
                                imageUri = uri
                                borderStatus = 2 // Photo Taken
                            },
                            onDismissImage = {
                                imageUri = null
                                borderStatus = 3 // Back to Live Stream
                            }
                        )

                        Button(
                            onClick = { flashMode = (flashMode + 1) % 3 }, // Cycle through 0, 1, 2
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(56.dp)
                        ) {
                            Text(
                                when (flashMode) {
                                    1 -> "Flash: On"
                                    2 -> "Flash: Auto"
                                    else -> "Flash: Off"
                                }
                            )
                        }

                        Spacer(modifier = Modifier.height(20.dp))

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly
                        ) {
                            Button(onClick = { borderStatus = 1 }) {
                                Text("Focused")
                            }
                            Button(onClick = { borderStatus = 2 }) {
                                Text("Photo Taken")
                            }
                            Button(onClick = { borderStatus = 3 }) {
                                Text("Live Stream")
                            }
                            Button(onClick = { borderStatus = 4 }) {
                                Text("Error")
                            }
                        }
                    }
                }
            }
        }
    }
}

@OptIn(ExperimentalGlideComposeApi::class)
@Composable
fun CamWinBorder(
    borderStatus: Int,
    hasCameraPermission: Boolean,
    flashMode: Int,
    imageCapture: ImageCapture?,
    imageUri: Uri?,
    onImageCaptured: (Uri) -> Unit,
    onDismissImage: () -> Unit
) {
    val context = LocalContext.current

    val borderColor = when (borderStatus) {
        1 -> Color.Green // Focused
        2 -> Color.Yellow // Photo Taken
        3 -> Color.Black // Live Stream
        4 -> Color.Red // Error
        else -> Color.Black
    }

    val configuration = LocalConfiguration.current
    val screenWidth = configuration.screenWidthDp.dp
    val height = screenWidth * 4 / 3

    Box(
        modifier = Modifier
            .width(screenWidth)
            .height(height)
            .background(Color.LightGray)
            .border(width = 2.dp, color = borderColor)
            .clickable {
                if (imageUri != null) {
                    onDismissImage()
                } else {
                    imageCapture?.let {
                        val name = SimpleDateFormat("yyyy-MM-dd-HH-mm-ss-SSS", Locale.US)
                            .format(System.currentTimeMillis())
                        val contentValues = ContentValues().apply {
                            put(MediaStore.MediaColumns.DISPLAY_NAME, name)
                            put(MediaStore.MediaColumns.MIME_TYPE, "image/jpeg")
                            if (Build.VERSION.SDK_INT > Build.VERSION_CODES.P) {
                                put(MediaStore.Images.Media.RELATIVE_PATH, "Pictures/CameraX-Image")
                            }
                        }
                        val outputOptions = ImageCapture.OutputFileOptions
                            .Builder(
                                context.contentResolver,
                                MediaStore.Images.Media.EXTERNAL_CONTENT_URI,
                                contentValues
                            )
                            .build()
                        it.takePicture(
                            outputOptions,
                            ContextCompat.getMainExecutor(context),
                            object : ImageCapture.OnImageSavedCallback {
                                override fun onError(exc: ImageCaptureException) {
                                    Log.e("CameraX", "Photo capture failed: ${exc.message}", exc)
                                }

                                override fun onImageSaved(output: ImageCapture.OutputFileResults) {
                                    Log.d("CameraX", "Photo capture succeeded: ${output.savedUri}")
                                    output.savedUri?.let(onImageCaptured)
                                }
                            }
                        )
                    }
                }
            }
    ) {
        if (imageUri != null) {
            GlideImage(
                model = imageUri,
                contentDescription = "Captured Image",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Fit
            )
        } else if (hasCameraPermission) {
            CameraPreview(flashMode, imageCapture)
        } else {
            Text("Camera permission not granted")
        }
    }
}

@Composable
fun CameraPreview(flashMode: Int, imageCapture: ImageCapture?) {
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraProviderFuture = remember { ProcessCameraProvider.getInstance(LocalContext.current) }
    var camera by remember { mutableStateOf<Camera?>(null) }

    AndroidView(
        factory = { ctx ->
            val previewView = PreviewView(ctx).apply {
                this.scaleType = PreviewView.ScaleType.FIT_CENTER
            }
            val executor = ContextCompat.getMainExecutor(ctx)
            cameraProviderFuture.addListener({
                val cameraProvider = cameraProviderFuture.get()
                val preview = CameraXPreview.Builder().build().also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }
                val cameraSelector = CameraSelector.Builder()
                    .requireLensFacing(CameraSelector.LENS_FACING_BACK)
                    .build()
                cameraProvider.unbindAll()
                camera = cameraProvider.bindToLifecycle(
                    lifecycleOwner,
                    cameraSelector,
                    preview,
                    imageCapture
                )
            }, executor)
            previewView
        },
        modifier = Modifier
            .fillMaxSize()
            .clip(RectangleShape),
        update = {
            camera?.cameraControl?.enableTorch(flashMode == 1)
            imageCapture?.flashMode = when (flashMode) {
                1 -> ImageCapture.FLASH_MODE_ON
                2 -> ImageCapture.FLASH_MODE_AUTO
                else -> ImageCapture.FLASH_MODE_OFF
            }
        }
    )
}

@Preview(showBackground = true, name = "Live Stream")
@Composable
fun CamWinBorderPreviewLive() {
    GpathTheme {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.TopCenter) {
            CamWinBorder(borderStatus = 3, hasCameraPermission = false, flashMode = 0, null, null, {}, {})
        }
    }
}

@Preview(showBackground = true, name = "Photo Taken")
@Composable
fun CamWinBorderPreviewTaken() {
    GpathTheme {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.TopCenter) {
            CamWinBorder(borderStatus = 2, hasCameraPermission = false, flashMode = 0, null, null, {}, {})
        }
    }
}

@Preview(showBackground = true, name = "Error")
@Composable
fun CamWinBorderPreviewError() {
    GpathTheme {
        Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.TopCenter) {
            CamWinBorder(borderStatus = 4, hasCameraPermission = false, flashMode = 0, null, null, {}, {})
        }
    }
}